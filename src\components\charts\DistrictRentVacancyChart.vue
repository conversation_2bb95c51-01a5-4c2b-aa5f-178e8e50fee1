<template>
  <div class="district-rent-vacancy-chart">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="区域租赁均价及空置率" />
        <div ref="rentVacancyChartRef" class="rent-vacancy-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, watch } from "vue";
import * as echarts from "echarts";
import ChartTitle from '@/components/common/ChartTitle.vue';

// 接收父组件传递的选中区域信息
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: "长沙市",
      level: "city",
      areaData: null
    })
  }
});

// 图表引用
const rentVacancyChartRef = ref(null);
let rentVacancyChart = null;

// 区域租赁均价及空置率数据配置（年度+星级组合数据）
const districtRentVacancyData = {
  芙蓉区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [65.2, 62.8],
        vacancyRates: [28.5, 30.2],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [72.8, 69.5],
        vacancyRates: [24.6, 26.1],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [85.4, 81.2],
        vacancyRates: [18.2, 19.8],
        color: "#66bb6a"
      },
      "7星顶级": {
        rentPrices: [92.6, 88.3],
        vacancyRates: [15.8, 17.2],
        color: "#4fc3f7"
      }
    }
  },
  开福区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [58.4, 55.8],
        vacancyRates: [32.1, 34.2],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [68.2, 64.9],
        vacancyRates: [26.4, 28.1],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [78.9, 75.2],
        vacancyRates: [20.5, 22.3],
        color: "#66bb6a"
      },
      "7星顶级": {
        rentPrices: [85.3, 81.6],
        vacancyRates: [18.2, 19.8],
        color: "#4fc3f7"
      }
    }
  },
  天心区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [55.8, 52.4],
        vacancyRates: [22.3, 24.1],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [65.4, 61.8],
        vacancyRates: [18.6, 20.2],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [75.2, 71.5],
        vacancyRates: [15.4, 16.8],
        color: "#66bb6a"
      },
      "7星顶级": {
        rentPrices: [82.7, 78.9],
        vacancyRates: [12.8, 14.2],
        color: "#4fc3f7"
      }
    }
  },
  望城区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [32.5, 32.5],
        vacancyRates: [35.2, 35.2],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [38.2, 38.2],
        vacancyRates: [29.1, 29.1],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [45.8, 45.8],
        vacancyRates: [24.6, 24.6],
        color: "#66bb6a"
      },
      "7星顶级": {
        rentPrices: [52.3, 52.3],
        vacancyRates: [21.8, 21.8],
        color: "#4fc3f7"
      }
    }
  },
  雨花区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [58.9, 55.2],
        vacancyRates: [21.8, 23.4],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [68.5, 64.8],
        vacancyRates: [17.3, 18.9],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [78.2, 74.5],
        vacancyRates: [14.2, 15.6],
        color: "#66bb6a"
      },
      "7星顶级": {
        rentPrices: [86.4, 82.7],
        vacancyRates: [11.9, 13.1],
        color: "#4fc3f7"
      }
    }
  },
  岳麓区: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [62.4, 58.7],
        vacancyRates: [20.5, 22.1],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [70.8, 67.1],
        vacancyRates: [17.3, 18.7],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [82.1, 78.4],
        vacancyRates: [13.8, 15.2],
        color: "#66bb6a"
      },
      "7星顶级": {
        rentPrices: [89.5, 85.8],
        vacancyRates: [10.6, 12.0],
        color: "#4fc3f7"
      }
    }
  },
  长沙县: {
    categories: ["2023年", "2024年"],
    series: {
      "4星标准级": {
        rentPrices: [42.8, 39.6],
        vacancyRates: [18.9, 20.3],
        color: "#ff7043"
      },
      "5星甲级": {
        rentPrices: [48.6, 45.4],
        vacancyRates: [15.3, 16.7],
        color: "#ffa726"
      },
      "6星超甲级": {
        rentPrices: [56.2, 53.0],
        vacancyRates: [12.4, 13.8],
        color: "#66bb6a"
      },
      "7星顶级": {
        rentPrices: [62.8, 59.6],
        vacancyRates: [9.8, 11.2],
        color: "#4fc3f7"
      }
    }
  }
};

// 动态数据
const chartData = computed(() => {
  const regionName = props.selectedRegion.regionName;
  return (
    districtRentVacancyData[regionName] || districtRentVacancyData["芙蓉区"]
  );
});

// 初始化图表
const initRentVacancyChart = () => {
  if (!rentVacancyChartRef.value) return;

  rentVacancyChart = echarts.init(rentVacancyChartRef.value);

  const option = {
    backgroundColor: "transparent",
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        crossStyle: {
          color: "#999"
        }
      },
      backgroundColor: "rgba(0, 20, 50, 0.8)",
      borderColor: "#4fc3f7",
      textStyle: { color: "#fff" },
      formatter: function(params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          if (param.seriesName === "租赁均价") {
            result += `<div>${param.marker} ${param.seriesName}: ${param.value}元/月/㎡</div>`;
          } else {
            result += `<div>${param.marker} ${param.seriesName}: ${param.value}%</div>`;
          }
        });
        return result;
      }
    },
    legend: [
      {
        // 第一行：租金图例
        data: ["4星标准级租金", "5星甲级租金", "6星超甲级租金", "7星顶级租金"],
        textStyle: {
          color: "#e3f2fd",
          fontSize: 10
        },
        bottom: 35,
        left: "center",
        itemWidth: 10,
        itemHeight: 6,
        itemGap: 15,
        orient: "horizontal"
      },
      {
        // 第二行：空置率图例
        data: ["4星标准级空置率", "5星甲级空置率", "6星超甲级空置率", "7星顶级空置率"],
        textStyle: {
          color: "#e3f2fd",
          fontSize: 10
        },
        bottom: 15,
        left: "center",
        itemWidth: 10,
        itemHeight: 6,
        itemGap: 15,
        orient: "horizontal"
      }
    ],
    grid: {
      left: "10%",
      right: "10%",
      bottom: "25%", // 增加底部空间给两行图例
      top: "15%", // 减少顶部空间，因为图例移到了底部
      containLabel: true
    },
    xAxis: [
      {
        type: "category",
        data: chartData.value.categories,
        axisPointer: {
          type: "shadow"
        },
        axisLabel: {
          color: "#e3f2fd",
          fontSize: 12
        },
        axisLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.3)"
          }
        }
      }
    ],
    yAxis: [
      {
        type: "value",
        name: "租赁均价(元/月/㎡)",
        position: "left",
        nameTextStyle: {
          color: "#e3f2fd",
          fontSize: 11
        },
        axisLabel: {
          color: "#e3f2fd",
          fontSize: 11,
          formatter: "{value}"
        },
        axisLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.3)"
          }
        },
        splitLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.1)"
          }
        }
      },
      {
        type: "value",
        name: "空置率(%)",
        position: "right",
        nameTextStyle: {
          color: "#e3f2fd",
          fontSize: 11
        },
        axisLabel: {
          color: "#e3f2fd",
          fontSize: 11,
          formatter: "{value}%"
        },
        axisLine: {
          lineStyle: {
            color: "rgba(79, 195, 247, 0.3)"
          }
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      // 4星标准级租赁均价
      {
        name: "4星标准级租金",
        type: "bar",
        yAxisIndex: 0,
        data: chartData.value.series["4星标准级"].rentPrices,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: chartData.value.series["4星标准级"].color },
            { offset: 1, color: chartData.value.series["4星标准级"].color + "4D" }
          ])
        },
        emphasis: {
          focus: "series"
        }
      },
      // 5星甲级租赁均价
      {
        name: "5星甲级租金",
        type: "bar",
        yAxisIndex: 0,
        data: chartData.value.series["5星甲级"].rentPrices,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: chartData.value.series["5星甲级"].color },
            { offset: 1, color: chartData.value.series["5星甲级"].color + "4D" }
          ])
        },
        emphasis: {
          focus: "series"
        }
      },
      // 6星超甲级租赁均价
      {
        name: "6星超甲级租金",
        type: "bar",
        yAxisIndex: 0,
        data: chartData.value.series["6星超甲级"].rentPrices,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: chartData.value.series["6星超甲级"].color },
            { offset: 1, color: chartData.value.series["6星超甲级"].color + "4D" }
          ])
        },
        emphasis: {
          focus: "series"
        }
      },
      // 7星顶级租赁均价
      {
        name: "7星顶级租金",
        type: "bar",
        yAxisIndex: 0,
        data: chartData.value.series["7星顶级"].rentPrices,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: chartData.value.series["7星顶级"].color },
            { offset: 1, color: chartData.value.series["7星顶级"].color + "4D" }
          ])
        },
        emphasis: {
          focus: "series"
        }
      },
      // 4星标准级空置率折线图
      {
        name: "4星标准级空置率",
        type: "line",
        yAxisIndex: 1,
        data: chartData.value.series["4星标准级"].vacancyRates,
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: chartData.value.series["4星标准级"].color,
          type: "dashed"
        },
        itemStyle: {
          color: chartData.value.series["4星标准级"].color
        },
        emphasis: {
          focus: "series"
        }
      },
      // 5星甲级空置率折线图
      {
        name: "5星甲级空置率",
        type: "line",
        yAxisIndex: 1,
        data: chartData.value.series["5星甲级"].vacancyRates,
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: chartData.value.series["5星甲级"].color,
          type: "dashed"
        },
        itemStyle: {
          color: chartData.value.series["5星甲级"].color
        },
        emphasis: {
          focus: "series"
        }
      },
      // 6星超甲级空置率折线图
      {
        name: "6星超甲级空置率",
        type: "line",
        yAxisIndex: 1,
        data: chartData.value.series["6星超甲级"].vacancyRates,
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: chartData.value.series["6星超甲级"].color,
          type: "dashed"
        },
        itemStyle: {
          color: chartData.value.series["6星超甲级"].color
        },
        emphasis: {
          focus: "series"
        }
      },
      // 7星顶级空置率折线图
      {
        name: "7星顶级空置率",
        type: "line",
        yAxisIndex: 1,
        data: chartData.value.series["7星顶级"].vacancyRates,
        smooth: true,
        symbol: "circle",
        symbolSize: 6,
        lineStyle: {
          width: 2,
          color: chartData.value.series["7星顶级"].color,
          type: "dashed"
        },
        itemStyle: {
          color: chartData.value.series["7星顶级"].color
        },
        emphasis: {
          focus: "series"
        }
      }
    ]
  };

  rentVacancyChart.setOption(option);
};

// 更新图表数据
const updateChart = () => {
  if (rentVacancyChart) {
    const option = {
      xAxis: [
        {
          data: chartData.value.categories
        }
      ],
      series: [
        {
          data: chartData.value.series["4星标准级"].rentPrices
        },
        {
          data: chartData.value.series["5星甲级"].rentPrices
        },
        {
          data: chartData.value.series["6星超甲级"].rentPrices
        },
        {
          data: chartData.value.series["7星顶级"].rentPrices
        },
        {
          data: chartData.value.series["4星标准级"].vacancyRates
        }
      ]
    };
    rentVacancyChart.setOption(option);
  }
};

// 处理窗口大小变化
const handleResize = () => {
  if (rentVacancyChart) {
    rentVacancyChart.resize();
  }
};

// 监听选中区域变化
watch(
  () => props.selectedRegion,
  () => {
    updateChart();
  },
  { deep: true }
);

onMounted(() => {
  nextTick(() => {
    initRentVacancyChart();
  });

  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("resize", handleResize);
  if (rentVacancyChart) {
    rentVacancyChart.dispose();
  }
});
</script>

<style scoped lang="less">
.district-rent-vacancy-chart {
  height: 100%;
}

.border-box {
  height: 100%;
  padding: 10px 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.section-title {
  position: relative;
  z-index: 1;
  color: #4fc3f7;
  font-size: 14px;
  font-weight: bold;
  margin-left: 40px;
  text-align: left;
  text-shadow: 0 0 10px rgba(79, 195, 247, 0.5);
  white-space: nowrap;
}

.rent-vacancy-chart {
  flex: 1;
  width: 100%;
  min-height: 200px;
}
</style>
