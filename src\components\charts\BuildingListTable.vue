<template>
  <div class="building-list-table">
    <div class="border-box">
      <div class="table-container">
        <ChartTitle title="楼宇列表">
          <template #extra v-if="selectedRegion.level === 'district'">
            <select v-model="selectedStarLevel" @change="filterData" class="title-filter-select">
              <option value="">全部星级</option>
              <option value="4星标准级">4星标准级</option>
              <option value="5星甲级">5星甲级</option>
              <option value="6星超甲级">6星超甲级</option>
              <option value="7星顶级">7星顶级</option>
            </select>
            <select v-model="selectedStreet" @change="filterData" class="title-filter-select">
              <option v-for="street in availableStreets" :key="street" :value="street">{{ street }}</option>
            </select>
          </template>
        </ChartTitle>
        
        <div class="custom-table">
          <div class="table-header">
            <div class="table-cell">名称</div>
            <div class="table-cell">等级</div>
            <div class="table-cell">{{ selectedRegion.level === 'city' ? '区域' : '街道' }}</div>
            <div class="table-cell">地址</div>
            <div class="table-cell">企业数量</div>
          </div>
          <div
            class="table-body"
            ref="tableBodyRef"
            @mouseenter="stopAutoScroll"
            @mouseleave="startAutoScroll"
          >
            <!-- 加载状态 -->
            <div v-if="loading" class="loading-state">
              <div class="loading-text">正在加载楼宇数据...</div>
            </div>

            <!-- 错误状态 -->
            <div v-else-if="error" class="error-state">
              <div class="error-text">{{ error }}</div>
            </div>

            <!-- 数据列表 -->
            <div
              v-else
              v-for="item in displayTableData"
              :key="item.name"
              class="table-row"
              @click="goToBuildingDetail(item)"
            >
              <div class="table-cell" :title="item.name">{{ item.name }}</div>
              <div class="table-cell">{{ item.level }}</div>
              <div class="table-cell">{{ selectedRegion.level === 'city' ? item.region : item.street }}</div>
              <div class="table-cell" :title="item.address">{{ item.address }}</div>
              <div class="table-cell">{{ item.companyCount }}</div>
            </div>

            <!-- 无数据状态 -->
            <div v-if="!loading && !error && displayTableData.length === 0" class="no-data-state">
              <div class="no-data-text">暂无楼宇数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import ChartTitle from '@/components/common/ChartTitle.vue';

const router = useRouter();

// 接收父组件传递的数据
const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({
      regionName: '长沙市',
      level: 'city',
      areaData: null
    })
  },
  buildingList: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  },
  error: {
    type: String,
    default: null
  }
});

// 表格滚动引用
const tableBodyRef = ref(null);

// 筛选器状态
const selectedStarLevel = ref('');
const selectedStreet = ref('');

// 市级数据配置
const cityLevelData = [
  {
    name: '佳兆业广场',
    level: '7星顶级',
    region: '芙蓉区',
    address: '五一大道123号',
    companyCount: 121
  },
  {
    name: '长房国际大厦',
    level: '7星顶级',
    region: '芙蓉区',
    address: '解放东路456号',
    companyCount: 234
  },
  {
    name: '华远国际中心',
    level: '6星超甲级',
    region: '天心区',
    address: '湘江中路789号',
    companyCount: 163
  },
  {
    name: '汇景发展环球中心',
    level: '6星超甲级',
    region: '天心区',
    address: '芙蓉南路321号',
    companyCount: 206
  },
  {
    name: '长沙国金中心',
    level: '7星顶级',
    region: '芙蓉区',
    address: '湘江中路888号',
    companyCount: 284
  },
  {
    name: '通程国际大酒店',
    level: '4星标准级',
    region: '芙蓉区',
    address: '五一广场东侧',
    companyCount: 125
  },
  {
    name: '友阿总部办公大楼',
    level: '5星甲级',
    region: '芙蓉区',
    address: '解放西路88号',
    companyCount: 106
  },
  {
    name: '宇成朝阳广场',
    level: '4星标准级',
    region: '芙蓉区',
    address: '朝阳路168号',
    companyCount: 84
  },
  {
    name: '红橡国际广场',
    level: '6星超甲级',
    region: '芙蓉区',
    address: '韶山北路99号',
    companyCount: 169
  },
  {
    name: '香泽南湖大厦',
    level: '4星标准级',
    region: '芙蓉区',
    address: '南湖路66号',
    companyCount: 93
  },
  {
    name: '保利国际广场',
    level: '5星甲级',
    region: '天心区',
    address: '湘府中路288号',
    companyCount: 124
  },
  {
    name: '蓝湾国际',
    level: '6星超甲级',
    region: '天心区',
    address: '芙蓉南路518号',
    companyCount: 187
  },
  {
    name: '御邦国际广场',
    level: '5星甲级',
    region: '天心区',
    address: '书院路128号',
    companyCount: 163
  },
  {
    name: '万达广场',
    level: '6星超甲级',
    region: '岳麓区',
    address: '金星中路666号',
    companyCount: 298
  },
  {
    name: '梅溪湖国际新城',
    level: '7星顶级',
    region: '岳麓区',
    address: '梅溪湖路888号',
    companyCount: 356
  }
];

// 区级数据配置（按区域分类）
const districtLevelData = {
  '芙蓉区': [
    {
      name: '佳兆业广场',
      level: '7星顶级',
      street: '五一路街道',
      address: '五一大道123号',
      companyCount: 121
    },
    {
      name: '长房国际大厦',
      level: '7星顶级',
      street: '解放路街道',
      address: '解放东路456号',
      companyCount: 234
    },
    {
      name: '长沙国金中心',
      level: '7星顶级',
      street: '湘湖街道',
      address: '湘江中路888号',
      companyCount: 284
    },
    {
      name: '通程国际大酒店',
      level: '4星标准级',
      street: '五一路街道',
      address: '五一广场东侧',
      companyCount: 125
    },
    {
      name: '友阿总部办公大楼',
      level: '5星甲级',
      street: '解放路街道',
      address: '解放西路88号',
      companyCount: 106
    },
    {
      name: '宇成朝阳广场',
      level: '4星标准级',
      street: '朝阳街道',
      address: '朝阳路168号',
      companyCount: 84
    },
    {
      name: '红橡国际广场',
      level: '6星超甲级',
      street: '韶山路街道',
      address: '韶山北路99号',
      companyCount: 169
    },
    {
      name: '香泽南湖大厦',
      level: '4星标准级',
      street: '南湖路街道',
      address: '南湖路66号',
      companyCount: 93
    }
  ],
  '天心区': [
    {
      name: '华远国际中心',
      level: '6星超甲级',
      street: '坡子街街道',
      address: '湘江中路789号',
      companyCount: 163
    },
    {
      name: '汇景发展环球中心',
      level: '6星超甲级',
      street: '裕南街街道',
      address: '芙蓉南路321号',
      companyCount: 206
    },
    {
      name: '保利国际广场',
      level: '5星甲级',
      street: '金盆岭街道',
      address: '湘府中路288号',
      companyCount: 124
    },
    {
      name: '蓝湾国际',
      level: '6星超甲级',
      street: '新开铺街道',
      address: '芙蓉南路518号',
      companyCount: 187
    }
  ],
  '岳麓区': [
    {
      name: '万达广场',
      level: '6星超甲级',
      street: '银盆岭街道',
      address: '金星中路666号',
      companyCount: 298
    },
    {
      name: '梅溪湖国际新城',
      level: '7星顶级',
      street: '梅溪湖街道',
      address: '梅溪湖路888号',
      companyCount: 356
    },
    {
      name: '中南大学科技园',
      level: '5星甲级',
      street: '岳麓街道',
      address: '麓山南路932号',
      companyCount: 189
    }
  ]
};

// 转换传递的数据格式为组件需要的格式
const convertBuildingData = (buildingList) => {
  return buildingList.map(item => ({
    name: item.buildingName,
    level: item.buildingLevel,
    region: item.districtName,
    street: item.street || '未知街道', // 如果有街道信息
    address: item.address,
    companyCount: item.companies,
    originalData: item
  }));
};

// 动态表格数据
const tableData = computed(() => {
  // 优先使用传递的数据
  if (props.buildingList && props.buildingList.length > 0) {
    return convertBuildingData(props.buildingList);
  }

  // 如果没有传递数据，使用静态数据作为后备
  if (props.selectedRegion.level === 'city') {
    return cityLevelData;
  } else {
    return districtLevelData[props.selectedRegion.regionName] || [];
  }
});

// 可用街道列表
const availableStreets = computed(() => {
  const streets = [...new Set(tableData.value.map(item => item.street))];
  return streets.sort();
});

// 显示的表格数据（根据级别和筛选条件）
const displayTableData = computed(() => {
  let data = tableData.value;
  
  // 仅在区级页面应用筛选
  if (props.selectedRegion.level === 'district') {
    data = data.filter(item => {
      const starMatch = !selectedStarLevel.value || item.level === selectedStarLevel.value;
      const streetMatch = !selectedStreet.value || item.street === selectedStreet.value;
      return starMatch && streetMatch;
    });
  }
  
  return data;
});

// 筛选数据函数
const filterData = () => {
  // 重新启动自动滚动
  stopAutoScroll();
  setTimeout(() => {
    startAutoScroll();
  }, 500);
};

// 监听区域变化，重置筛选器
watch(() => props.selectedRegion, () => {
  selectedStarLevel.value = '';
  selectedStreet.value = '';
  
  // 重新启动自动滚动
  stopAutoScroll();
  setTimeout(() => {
    startAutoScroll();
  }, 500);
}, { deep: true });

// 自动滚动相关变量
let scrollInterval = null;

// 自动滚动函数
const startAutoScroll = () => {
  if (!tableBodyRef.value) return;

  scrollInterval = setInterval(() => {
    const tableBody = tableBodyRef.value;

    // 添加更严格的空值检查
    if (!tableBody || !tableBody.scrollHeight || !tableBody.clientHeight) {
      return;
    }

    const scrollHeight = tableBody.scrollHeight;
    const clientHeight = tableBody.clientHeight;
    const currentScrollTop = tableBody.scrollTop;

    // 如果已经滚动到底部，回到顶部
    if (currentScrollTop + clientHeight >= scrollHeight - 5) {
      tableBody.scrollTop = 0;
    } else {
      // 平滑滚动
      tableBody.scrollTop += 1;
    }
  }, 50); // 每50ms滚动1px，实现平滑滚动
};

// 停止自动滚动
const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval);
    scrollInterval = null;
  }
};

// 跳转到楼宇详情页
const goToBuildingDetail = (building) => {
  // 提取星级数字
  const starMatch = building.level.match(/(\d+)星级/);
  const star = starMatch ? parseInt(starMatch[1]) : 5;

  const buildingId = Date.now();
  router.push({
    path: `/building/${buildingId}`,
    query: {
      buildingId: buildingId,
      buildingName: building.name,
      district: props.selectedRegion.level === 'city' ? building.region : props.selectedRegion.regionName,
      street: building.street || '未知街道',
      star: star,
      address: building.address
    }
  });
};

onMounted(async () => {
  // 等待DOM完全渲染后再启动自动滚动
  await nextTick();
  setTimeout(() => {
    if (tableBodyRef.value) {
      startAutoScroll();
    }
  }, 1000); // 延迟1秒开始滚动
});

onUnmounted(() => {
  stopAutoScroll();
});
</script>

<style scoped lang="less">
.building-list-table {
  height: 100%;
}

.border-box {
  height: 100%;
  background-size: 100% 100%;
  padding: 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.table-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  width: 26vw; /* 使用vw单位固定宽度 */
}

/* 原有的标题样式已移至ChartTitle组件 */

/* 表格样式 */
.custom-table {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%; /* 铺满容器宽度 */
  table-layout: fixed; /* 固定表格布局 */
}

.table-header {
  display: flex;
  background: rgba(79, 195, 247, 0.1);
  border-radius: 8px 8px 0 0;
  padding: 8px 0;
  margin-bottom: 5px;
  width: 100%; /* 铺满容器宽度 */
  flex-shrink: 0; /* 防止收缩 */
}

.table-body {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  &::-webkit-scrollbar {
    display: none;
  }
}

.table-cell {
  padding: 8px 2px;
  color: #e3f2fd;
  font-size: 11px;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 设置各列的具体宽度比例 */
.table-cell:nth-child(1) { /* 名称 */
  flex: 2.5; /* 楼宇名称，较宽 */
}

.table-cell:nth-child(2) { /* 等级 */
  flex: 1; /* 星级信息 */
}

.table-cell:nth-child(3) { /* 区域 */
  flex: 1; /* 区域名称 */
}

.table-cell:nth-child(4) { /* 地址 */
  flex: 3; /* 地址信息，最宽 */
  text-align: left; /* 地址左对齐更易读 */
}

.table-cell:nth-child(5) { /* 企业数量 */
  flex: 1; /* 企业数量 */
}

.table-header .table-cell {
  font-weight: bold;
  color: #4fc3f7;
  background: rgba(79, 195, 247, 0.1);
}

.table-row {
  display: flex;
  border-bottom: 1px solid rgba(79, 195, 247, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  width: 100%; /* 铺满容器宽度 */
  flex-shrink: 0; /* 防止收缩 */

  &:hover {
    background: rgba(79, 195, 247, 0.15);
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(79, 195, 247, 0.2);
  }
}

.table-row:last-child {
  border-bottom: none;
}

/* 状态样式 */
.loading-state,
.error-state,
.no-data-state {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100px;
  color: #4fc3f7;
  font-size: 14px;
}

.error-state {
  color: #f44336;
}

.loading-text,
.error-text,
.no-data-text {
  text-align: center;
}
</style>
