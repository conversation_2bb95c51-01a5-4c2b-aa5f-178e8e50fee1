<template>
  <div class="rent-trend-line-chart">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="星级楼宇租金趋势" />
        <div ref="lineChartRef" class="line-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 折线图引用
const lineChartRef = ref(null);
let lineChart = null;

// 租金趋势数据配置
const rentTrendData = ref({
  quarters: ['2023Q1', '2023Q2', '2023Q3', '2023Q4', '2024Q1', '2024Q2', '2024Q3', '2024Q4'],
  starLevels: ['4星标准级', '5星甲级', '6星超甲级', '7星顶级'],
  data: {
    '4星标准级': [45, 47, 46, 48, 50, 52, 51, 53],
    '5星甲级': [65, 68, 67, 70, 72, 75, 74, 77],
    '6星超甲级': [85, 88, 87, 90, 93, 96, 95, 98],
    '7星顶级': [120, 125, 123, 128, 132, 136, 135, 140]
  }
});

// 初始化折线图
const initLineChart = () => {
  if (!lineChartRef.value) return;

  lineChart = echarts.init(lineChartRef.value);

  // 准备图表数据
  const quarters = rentTrendData.value.quarters;
  const starLevels = rentTrendData.value.starLevels;
  const data = rentTrendData.value.data;

  // 构建系列数据
  const series = starLevels.map((level, index) => ({
    name: level,
    type: 'line',
    data: data[level],
    smooth: true,
    symbol: 'circle',
    symbolSize: 6,
    lineStyle: {
      width: 2,
      color: ['#ff7043', '#ffa726', '#66bb6a', '#4fc3f7'][index]
    },
    itemStyle: {
      color: ['#ff7043', '#ffa726', '#66bb6a', '#4fc3f7'][index]
    },
    areaStyle: {
      opacity: 0.1,
      color: ['#ff7043', '#ffa726', '#66bb6a', '#4fc3f7'][index]
    },
    emphasis: {
      focus: 'series'
    }
  }));

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 20, 50, 0.8)',
      borderColor: '#4fc3f7',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          result += `<div>${param.marker} ${param.seriesName}: ${param.value}元/㎡/月</div>`;
        });
        return result;
      }
    },
    legend: {
      data: starLevels,
      textStyle: {
        color: '#e3f2fd',
        fontSize: 10
      },
      top: 10,
      itemWidth: 12,
      itemHeight: 8
    },
    grid: {
      left: '8%',
      right: '8%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: quarters,
      axisLabel: {
        color: '#e3f2fd',
        fontSize: 9,
        rotate: 45
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.3)'
        }
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '租金(元/㎡/月)',
      nameTextStyle: {
        color: '#e3f2fd',
        fontSize: 10
      },
      axisLabel: {
        color: '#e3f2fd',
        fontSize: 9
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.3)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.1)'
        }
      }
    },
    series: series
  };

  lineChart.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (lineChart) {
    lineChart.resize();
  }
};

// 模拟数据更新
const updateData = () => {
  const quarters = rentTrendData.value.quarters;
  const starLevels = rentTrendData.value.starLevels;
  
  starLevels.forEach(level => {
    rentTrendData.value.data[level] = rentTrendData.value.data[level].map(value => {
      const change = Math.floor(Math.random() * 6) - 3; // -3 到 +3 的随机变化
      return Math.max(0, value + change);
    });
  });

  // 更新折线图
  if (lineChart) {
    const series = starLevels.map((level, index) => ({
      name: level,
      type: 'line',
      data: rentTrendData.value.data[level],
      smooth: true,
      symbol: 'circle',
      symbolSize: 6,
      lineStyle: {
        width: 2,
        color: ['#ff7043', '#ffa726', '#66bb6a', '#4fc3f7'][index]
      },
      itemStyle: {
        color: ['#ff7043', '#ffa726', '#66bb6a', '#4fc3f7'][index]
      },
      areaStyle: {
        opacity: 0.1,
        color: ['#ff7043', '#ffa726', '#66bb6a', '#4fc3f7'][index]
      },
      emphasis: {
        focus: 'series'
      }
    }));
    lineChart.setOption({ series: series });
  }
};

let dataUpdateInterval = null;

onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initLineChart();
  });

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);

  // 数据更新逻辑
  dataUpdateInterval = setInterval(() => {
    updateData();
  }, 5000); // 每5秒更新一次数据
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (dataUpdateInterval) {
    clearInterval(dataUpdateInterval);
  }
  if (lineChart) {
    lineChart.dispose();
  }
});
</script>

<style scoped lang="less">
.rent-trend-line-chart {
  height: 100%;
}

.border-box {
  height: 100%;
  padding: 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.line-chart {
  flex: 1;
  width: 100%;
  min-height: 200px;
}
</style>
