<template>
  <div class="rent-trend-line-chart">
    <div class="border-box">
      <div class="chart-container">
        <ChartTitle title="星级楼宇租金趋势" />
        <div ref="lineChartRef" class="line-chart"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue';
import * as echarts from 'echarts';
import ChartTitle from '@/components/common/ChartTitle.vue';

// 折线图引用
const lineChartRef = ref(null);
let lineChart = null;

// 租金趋势数据配置 - 年度对比
const rentTrendData = ref({
  years: ['2023年', '2024年'],
  starLevels: ['4星标准级', '5星甲级', '6星超甲级','7星顶级'],
  data: {
    '4星标准级': [61.6, 57.6],
    '5星甲级': [74.6, 72.4],
    '6星超甲级': [92.7, 82.3],
    '7星顶级':[0,0]
  }
});

// 初始化折线图
const initLineChart = () => {
  if (!lineChartRef.value) return;

  lineChart = echarts.init(lineChartRef.value);

  // 准备图表数据
  const years = rentTrendData.value.years;
  const starLevels = rentTrendData.value.starLevels;
  const data = rentTrendData.value.data;

  // 构建系列数据
  const series = starLevels.map((level, index) => ({
    name: level,
    type: 'line',
    data: data[level],
    smooth: true,
    symbol: 'circle',
    symbolSize: 8,
    lineStyle: {
      width: 3,
      color: ['#ff7043', '#ffa726', '#66bb6a'][index]
    },
    itemStyle: {
      color: ['#ff7043', '#ffa726', '#66bb6a'][index]
    },
    areaStyle: {
      opacity: 0.1,
      color: ['#ff7043', '#ffa726', '#66bb6a'][index]
    },
    emphasis: {
      focus: 'series'
    }
  }));

  const option = {
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'rgba(0, 20, 50, 0.8)',
      borderColor: '#4fc3f7',
      textStyle: { color: '#fff' },
      formatter: function(params) {
        let result = `<div style="font-weight: bold; margin-bottom: 5px;">${params[0].axisValue}</div>`;
        params.forEach(param => {
          result += `<div>${param.marker} ${param.seriesName}: ${param.value}元/㎡/月</div>`;
        });
        return result;
      }
    },
    legend: {
      data: starLevels,
      textStyle: {
        color: '#e3f2fd',
        fontSize: 12
      },
      top: 10,
      itemWidth: 15,
      itemHeight: 10
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: years,
      axisLabel: {
        color: '#e3f2fd',
        fontSize: 12,
        rotate: 0
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.3)'
        }
      },
      splitLine: {
        show: false
      }
    },
    yAxis: {
      type: 'value',
      name: '租金(元/㎡/月)',
      nameTextStyle: {
        color: '#e3f2fd',
        fontSize: 12
      },
      axisLabel: {
        color: '#e3f2fd',
        fontSize: 10
      },
      axisLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.3)'
        }
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(79, 195, 247, 0.1)'
        }
      }
    },
    series: series
  };

  lineChart.setOption(option);
};

// 处理窗口大小变化
const handleResize = () => {
  if (lineChart) {
    lineChart.resize();
  }
};

// 数据更新函数（使用固定数据，无需动态更新）
const updateData = () => {
  // 使用固定的年度对比数据，无需随机更新
  console.log('租金趋势数据已加载:', rentTrendData.value);
};

onMounted(() => {
  // 初始化图表
  nextTick(() => {
    initLineChart();
    updateData(); // 初始化时调用一次数据更新
  });

  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  window.removeEventListener('resize', handleResize);
  if (lineChart) {
    lineChart.dispose();
  }
});
</script>

<style scoped lang="less">
.rent-trend-line-chart {
  height: 100%;
}

.border-box {
  height: 100%;
  padding: 20px;
  border-radius: 12px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

.line-chart {
  flex: 1;
  width: 100%;
  min-height: 200px;
}
</style>
