<template>
  <div class="green-building-chart">
    <div class="border-box">
      <div class="chart-container">
        <!-- 标题和切换器 -->
        <div class="title-section">
          <ChartTitle title="绿色建筑认证" />
          <!-- 认证标准切换 -->
          <div class="standard-switch">
            <div
              class="switch-item"
              :class="{ active: currentStandard === 'domestic' }"
              @click="switchStandard('domestic')"
            >
              国内认证
            </div>
            <div
              class="switch-item"
              :class="{ active: currentStandard === 'leed' }"
              @click="switchStandard('leed')"
            >
              LEED认证
            </div>
          </div>
        </div>

        <div class="certification-content">

          <!-- 认证等级分布 -->
          <div class="certification-levels">
            <div
              v-for="(level, index) in certificationData"
              :key="index"
              class="level-item"
            >
              <div class="level-header">
                <div class="level-icon" :class="level.class">
                  <span class="icon">{{ level.icon }}</span>
                </div>
                <div class="level-info">
                  <div class="level-name">{{ level.name }}</div>
                  <div class="level-desc">{{ level.description }}</div>
                </div>
                <div class="level-count">
                  <span class="count">{{ level.count }}</span>
                  <span class="unit">栋</span>
                </div>
              </div>

              <div class="level-progress">
                <div class="progress-bar">
                  <div
                    class="progress-fill"
                    :class="level.class"
                    :style="{ width: level.percentage + '%' }"
                  ></div>
                </div>
                <div class="percentage">{{ level.percentage }}%</div>
              </div>
            </div>
          </div>

          <!-- 底部统计信息 -->
          <!-- <div class="bottom-stats">
            <div class="stat-compact">
              <span class="stat-label-compact">已认证</span>
              <span class="stat-value-compact">{{ totalCertified }}栋</span>
            </div>
            <div class="stat-compact">
              <span class="stat-label-compact">认证率</span>
              <span class="stat-value-compact">{{ certificationRate }}%</span>
            </div>
            <div class="stat-compact">
              <span class="stat-label-compact">新增</span>
              <span class="stat-value-compact">+{{ newCertifications }}栋</span>
            </div>
            <div class="stat-compact">
              <span class="stat-label-compact">升级</span>
              <span class="stat-value-compact">+{{ upgradedCertifications }}栋</span>
            </div>
          </div> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import ChartTitle from '@/components/common/ChartTitle.vue';

const props = defineProps({
  selectedRegion: {
    type: Object,
    default: () => ({})
  },
  greenAuthList: {
    type: Array,
    default: () => []
  },
  leedAuthList: {
    type: Array,
    default: () => []
  }
});

// 当前认证标准
const currentStandard = ref('domestic');

// 处理国内认证数据
const domesticCertificationData = computed(() => {
  if (!props.greenAuthList || props.greenAuthList.length === 0) {
    return [
      {
        name: '暂无数据',
        description: '无认证信息',
        icon: '⚪',
        class: 'uncertified',
        count: 0,
        percentage: 0
      }
    ];
  }

  // 计算总数用于百分比计算
  const total = props.greenAuthList.reduce((sum, item) => sum + (item.count || 0), 0);

  return props.greenAuthList.map(item => {
    const percentage = total > 0 ? Math.round((item.count / total) * 100) : 0;

    // 根据等级名称设置图标和样式
    let icon = '⚪';
    let className = 'uncertified';

    if (item.levelName.includes('三星') || item.levelName.includes('3星')) {
      icon = '⭐';
      className = 'three-star';
    } else if (item.levelName.includes('二星') || item.levelName.includes('2星')) {
      icon = '🌟';
      className = 'two-star';
    } else if (item.levelName.includes('一星') || item.levelName.includes('1星')) {
      icon = '✨';
      className = 'one-star';
    }

    return {
      name: item.levelName,
      description: `占比${item.ratio || percentage}%`,
      icon: icon,
      class: className,
      count: item.count || 0,
      percentage: item.ratio || percentage
    };
  });
});

// 处理LEED认证数据
const leedCertificationData = computed(() => {
  if (!props.leedAuthList || props.leedAuthList.length === 0) {
    return [
      {
        name: '暂无数据',
        description: '无认证信息',
        icon: '⚪',
        class: 'uncertified',
        count: 0,
        percentage: 0
      }
    ];
  }

  // 计算总数用于百分比计算
  const total = props.leedAuthList.reduce((sum, item) => sum + (item.count || 0), 0);

  return props.leedAuthList.map(item => {
    const percentage = total > 0 ? Math.round((item.count / total) * 100) : 0;

    // 根据等级名称设置图标和样式
    let icon = '⚪';
    let className = 'uncertified';

    if (item.levelName.includes('铂金') || item.levelName.toLowerCase().includes('platinum')) {
      icon = '💎';
      className = 'platinum';
    } else if (item.levelName.includes('金') || item.levelName.toLowerCase().includes('gold')) {
      icon = '�';
      className = 'gold';
    } else if (item.levelName.includes('银') || item.levelName.toLowerCase().includes('silver')) {
      icon = '🥈';
      className = 'silver';
    } else if (item.levelName.includes('认证') || item.levelName.toLowerCase().includes('certified')) {
      icon = '🏅';
      className = 'certified';
    }

    return {
      name: item.levelName,
      description: `占比${item.ratio || percentage}%`,
      icon: icon,
      class: className,
      count: item.count || 0,
      percentage: item.ratio || percentage
    };
  });
});

// 动态认证数据
const certificationData = computed(() => {
  return currentStandard.value === 'domestic' ? domesticCertificationData.value : leedCertificationData.value;
});

// 切换认证标准
const switchStandard = (standard) => {
  currentStandard.value = standard;
};
</script>

<style scoped lang="less">
.green-building-chart {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.border-box {
  height: 100%;
  border-radius: 12px;
  padding: 15px;
  position: relative;
  overflow: hidden;
}

.chart-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* 原有的标题样式已移至ChartTitle组件 */

/* 标题区域布局 */
.title-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

/* 认证标准切换 */
.standard-switch {
  display: flex;
  background: rgba(79, 195, 247, 0.1);
  border-radius: 6px;
  padding: 2px;
  flex-shrink: 0;
}

.switch-item {
  padding: 4px 10px;
  text-align: center;
  font-size: 10px;
  color: #81d4fa;
  cursor: pointer;
  border-radius: 4px;
  transition: all 0.3s ease;
  white-space: nowrap;
  min-width: 50px;
}

.switch-item:hover {
  color: #4fc3f7;
}

.switch-item.active {
  background: #4fc3f7;
  color: #ffffff;
  font-weight: bold;
  box-shadow: 0 2px 8px rgba(79, 195, 247, 0.3);
}

.certification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 认证等级分布 */
.certification-levels {
  flex: 1;
  overflow-y: auto;
  padding-right: 5px;
  margin-bottom: 10px;
}

.level-item {
  margin-bottom: 8px;
  padding: 6px;
  background: rgba(79, 195, 247, 0.05);
  border: 1px solid rgba(79, 195, 247, 0.2);
  border-radius: 6px;
}

.level-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 6px;
}

.level-icon {
  width: 26px;
  height: 26px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.level-icon.three-star {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.level-icon.two-star {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
}

.level-icon.one-star {
  background: linear-gradient(135deg, #cd7f32, #daa520);
}

.level-icon.uncertified {
  background: linear-gradient(135deg, #757575, #9e9e9e);
}

/* LEED认证等级样式 */
.level-icon.platinum {
  background: linear-gradient(135deg, #e8eaf6, #c5cae9);
}

.level-icon.gold {
  background: linear-gradient(135deg, #ffd700, #ffed4e);
}

.level-icon.silver {
  background: linear-gradient(135deg, #c0c0c0, #e8e8e8);
}

.level-icon.certified {
  background: linear-gradient(135deg, #cd7f32, #daa520);
}

.level-icon .icon {
  font-size: 14px;
}

.level-info {
  flex: 1;
}

.level-name {
  color: #e3f2fd;
  font-size: 12px;
  font-weight: 500;
}

.level-desc {
  color: #81d4fa;
  font-size: 9px;
  opacity: 0.8;
}

.level-count {
  display: flex;
  align-items: baseline;
  gap: 2px;
}

.level-count .count {
  color: #66bb6a;
  font-size: 14px;
  font-weight: bold;
}

.level-count .unit {
  color: #81c784;
  font-size: 9px;
}

.level-progress {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-bar {
  flex: 1;
  height: 5px;
  background: rgba(79, 195, 247, 0.2);
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.progress-fill.three-star {
  background: linear-gradient(90deg, #ffd700, #ffed4e);
}

.progress-fill.two-star {
  background: linear-gradient(90deg, #c0c0c0, #e8e8e8);
}

.progress-fill.one-star {
  background: linear-gradient(90deg, #cd7f32, #daa520);
}

.progress-fill.uncertified {
  background: linear-gradient(90deg, #757575, #9e9e9e);
}

/* LEED认证进度条样式 */
.progress-fill.platinum {
  background: linear-gradient(90deg, #e8eaf6, #c5cae9);
}

.progress-fill.gold {
  background: linear-gradient(90deg, #ffd700, #ffed4e);
}

.progress-fill.silver {
  background: linear-gradient(90deg, #c0c0c0, #e8e8e8);
}

.progress-fill.certified {
  background: linear-gradient(90deg, #cd7f32, #daa520);
}

.percentage {
  color: #4fc3f7;
  font-size: 10px;
  font-weight: bold;
  min-width: 28px;
  text-align: right;
}

/* 底部紧凑统计 */
.bottom-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  padding: 8px;
  background: rgba(76, 175, 80, 0.05);
  border: 1px solid rgba(76, 175, 80, 0.2);
  border-radius: 6px;
  margin-top: auto;
}

.stat-compact {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  padding: 4px;
}

.stat-label-compact {
  color: #81c784;
  font-size: 9px;
  opacity: 0.8;
}

.stat-value-compact {
  color: #66bb6a;
  font-size: 11px;
  font-weight: bold;
  text-shadow: 0 0 6px rgba(102, 187, 106, 0.4);
}

/* 滚动条样式 */
.certification-levels::-webkit-scrollbar {
  width: 4px;
}

.certification-levels::-webkit-scrollbar-track {
  background: rgba(79, 195, 247, 0.1);
  border-radius: 2px;
}

.certification-levels::-webkit-scrollbar-thumb {
  background: rgba(79, 195, 247, 0.5);
  border-radius: 2px;
}

.certification-levels::-webkit-scrollbar-thumb:hover {
  background: rgba(79, 195, 247, 0.7);
}
</style>
